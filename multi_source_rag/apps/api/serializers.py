"""
Serializers for the API app.
"""

from apps.documents.models import DocumentChunk
from apps.search.models import ResultCitation, SearchQuery, SearchResult
from rest_framework import serializers


class SearchQuerySerializer(serializers.ModelSerializer):
    """
    Serializer for search queries.
    """

    class Meta:
        model = SearchQuery
        fields = ["id", "query_text", "timestamp", "search_params"]
        read_only_fields = ["id", "timestamp"]


class DocumentChunkSerializer(serializers.ModelSerializer):
    """
    Serializer for document chunks.
    """

    document_title = serializers.SerializerMethodField()
    document_url = serializers.SerializerMethodField()
    source_type = serializers.SerializerMethodField()

    class Meta:
        model = DocumentChunk
        fields = [
            "id",
            "text",
            "chunk_index",
            "document_title",
            "document_url",
            "source_type",
        ]

    def get_document_title(self, obj):
        return obj.document.title if obj.document else None

    def get_document_url(self, obj):
        return obj.document.permalink if obj.document else None

    def get_source_type(self, obj):
        return (
            obj.document.source.source_type
            if obj.document and obj.document.source
            else None
        )


class ResultCitationSerializer(serializers.ModelSerializer):
    """
    Serializer for result citations.
    """

    document_chunk = DocumentChunkSerializer()

    class Meta:
        model = ResultCitation
        fields = ["id", "document_chunk", "relevance_score", "rank"]


class SearchResultSerializer(serializers.ModelSerializer):
    """
    Serializer for search results.
    """

    citations = ResultCitationSerializer(many=True, read_only=True)

    class Meta:
        model = SearchResult
        fields = [
            "id",
            "generated_answer",
            "timestamp",
            "retriever_score_avg",
            "llm_confidence_score",
            "citations",
        ]
        read_only_fields = ["id", "timestamp"]


class SearchRequestSerializer(serializers.Serializer):
    """
    Serializer for search requests.
    """

    query = serializers.CharField(required=True, help_text="The search query")
    top_k = serializers.IntegerField(
        required=False, default=20, min_value=1, max_value=50,
        help_text="Number of results to return (1-50, default: 20)"
    )
    tenant_slug = serializers.CharField(
        required=False, help_text="Tenant slug to search in"
    )
    filter = serializers.DictField(
        required=False, help_text="Filter to apply to search results"
    )

    # Advanced search parameters
    use_hybrid_search = serializers.BooleanField(
        required=False,
        default=True,
        help_text="Whether to use hybrid search (BM25 + vector)",
    )
    use_enhanced_prompts = serializers.BooleanField(
        required=False, default=True, help_text="Whether to use query classification and enhanced prompts"
    )
    use_query_expansion = serializers.BooleanField(
        required=False, default=False, help_text="Whether to use query expansion"
    )
    use_multi_step_reasoning = serializers.BooleanField(
        required=False, default=False, help_text="Whether to use multi-step reasoning"
    )
    output_format = serializers.ChoiceField(
        required=False,
        default="text",
        choices=["text", "json", "markdown", "table"],
        help_text="Output format for the response",
    )
    min_relevance_score = serializers.FloatField(
        required=False,
        default=0.4,
        min_value=0.0,
        max_value=1.0,
        help_text="Minimum relevance score for documents (0.0 to 1.0, default: 0.4)",
    )
