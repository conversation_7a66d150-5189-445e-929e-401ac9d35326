{% extends 'base.html' %}

{% block title %}Search - Multi-Source RAG{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        max-width: 900px;
        margin: 0 auto;
    }

    .search-box {
        border-radius: 12px;
        padding: 18px 24px;
        font-size: 1.1rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        border-color: #007bff;
        box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
        transform: translateY(-1px);
    }

    .search-button {
        border-radius: 12px;
        padding: 18px 32px;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .search-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }

    .search-button:disabled {
        transform: none;
        box-shadow: none;
    }

    .conversation-card {
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
        border-radius: 8px;
    }

    .conversation-card:hover {
        transform: translateY(-2px);
        border-left-color: #007bff;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .advanced-options {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        border: 1px solid #e9ecef;
    }

    .source-filter .btn-check:checked + .btn {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-content {
        text-align: center;
        padding: 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .search-suggestions {
        margin-top: 1rem;
    }

    .suggestion-chip {
        display: inline-block;
        background: #e9ecef;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.9rem;
    }

    .suggestion-chip:hover {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: translateY(-1px);
    }

    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin: -2rem -15px 2rem -15px;
        border-radius: 0 0 20px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        .hero-subtitle {
            font-size: 1rem;
        }
        .search-box {
            font-size: 1rem;
            padding: 15px 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="text-center">
            <h1 class="hero-title">
                <i class="fas fa-search me-3"></i>
                Multi-Source RAG Search
            </h1>
            <p class="hero-subtitle">
                Intelligent search across Slack, Confluence, Google Docs, and GitHub repositories
            </p>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="search-container">
            <!-- Main Search Form -->
            <form method="post" action="{% url 'search:query' %}" class="mb-4" id="searchForm">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-10">
                        <input type="text" name="query" class="form-control search-box"
                               placeholder="Ask anything about your codebase, documentation, or team discussions..."
                               value="{{ query|default:'' }}" required
                               aria-label="Search query"
                               autocomplete="off">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary search-button w-100" id="searchBtn">
                            <i class="fas fa-search me-2"></i>
                            <span class="btn-text">Search</span>
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </div>

                <!-- Advanced Options Toggle -->
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-link" data-bs-toggle="collapse" data-bs-target="#advancedOptions"
                            aria-expanded="false" aria-controls="advancedOptions">
                        <i class="fas fa-cog me-2"></i>Advanced Options
                        <i class="fas fa-chevron-down ms-2"></i>
                    </button>
                </div>

                <!-- Advanced Options Panel -->
                <div class="collapse" id="advancedOptions">
                    <div class="advanced-options">
                        <div class="row">
                            <!-- Source Filters -->
                            <div class="col-md-6">
                                <h6 class="mb-3">
                                    <i class="fas fa-filter me-2"></i>Filter by Source
                                </h6>
                                <div class="source-filter">
                                    <input type="checkbox" class="btn-check" name="source_filter" value="slack" id="slack">
                                    <label class="btn btn-outline-primary btn-sm me-2 mb-2" for="slack">
                                        <i class="fab fa-slack me-1"></i>Slack
                                    </label>

                                    <input type="checkbox" class="btn-check" name="source_filter" value="confluence" id="confluence">
                                    <label class="btn btn-outline-primary btn-sm me-2 mb-2" for="confluence">
                                        <i class="fab fa-confluence me-1"></i>Confluence
                                    </label>

                                    <input type="checkbox" class="btn-check" name="source_filter" value="google_docs" id="google_docs">
                                    <label class="btn btn-outline-primary btn-sm me-2 mb-2" for="google_docs">
                                        <i class="fab fa-google me-1"></i>Google Docs
                                    </label>

                                    <input type="checkbox" class="btn-check" name="source_filter" value="github" id="github">
                                    <label class="btn btn-outline-primary btn-sm me-2 mb-2" for="github">
                                        <i class="fab fa-github me-1"></i>GitHub
                                    </label>
                                </div>
                            </div>

                            <!-- Search Options -->
                            <div class="col-md-6">
                                <h6 class="mb-3">
                                    <i class="fas fa-brain me-2"></i>Search Techniques
                                </h6>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="use_hybrid_search" value="true" id="hybridSearch" checked>
                                    <label class="form-check-label" for="hybridSearch">
                                        <strong>Hybrid Search</strong>
                                        <small class="text-muted d-block">Combine semantic and keyword search</small>
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="use_enhanced_prompts" value="true" id="enhancedPrompts" checked>
                                    <label class="form-check-label" for="enhancedPrompts">
                                        <strong>Enhanced Prompts</strong>
                                        <small class="text-muted d-block">Use query classification and specialized prompts</small>
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="use_query_expansion" value="true" id="queryExpansion">
                                    <label class="form-check-label" for="queryExpansion">
                                        <strong>Query Expansion</strong>
                                        <small class="text-muted d-block">Expand query with related terms</small>
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="use_multi_step_reasoning" value="true" id="multiStepReasoning">
                                    <label class="form-check-label" for="multiStepReasoning">
                                        <strong>Multi-Step Reasoning</strong>
                                        <small class="text-muted d-block">Break down complex queries</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Search Suggestions -->
            <div class="search-suggestions">
                <h6 class="text-muted mb-3">
                    <i class="fas fa-lightbulb me-2"></i>Try these example searches:
                </h6>
                <div class="suggestion-chips">
                    <span class="suggestion-chip" data-query="What are the main engineering challenges discussed?">
                        Engineering challenges
                    </span>
                    <span class="suggestion-chip" data-query="Show me recent bug reports and issues">
                        Bug reports
                    </span>
                    <span class="suggestion-chip" data-query="Summarize feedback from customers">
                        Customer feedback
                    </span>
                    <span class="suggestion-chip" data-query="What testing strategies are being used?">
                        Testing strategies
                    </span>
                    <span class="suggestion-chip" data-query="Show me documentation about API endpoints">
                        API documentation
                    </span>
                    <span class="suggestion-chip" data-query="What are the performance optimization discussions?">
                        Performance optimization
                    </span>
                </div>
            </div>

            {% if recent_conversations %}
                <h3 class="mt-5 mb-3">Recent Conversations</h3>
                <div class="list-group">
                    {% for conversation in recent_conversations %}
                        <a href="{% url 'search:conversation_detail' conversation.id %}" class="list-group-item list-group-item-action conversation-card">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ conversation.title }}</h5>
                                <small>{{ conversation.updated_at|date:"M d, Y" }}</small>
                            </div>
                            {% with last_message=conversation.messages.last %}
                                {% if last_message %}
                                    <p class="mb-1">
                                        <strong>{% if last_message.is_user %}You{% else %}Assistant{% endif %}:</strong>
                                        {{ last_message.content|truncatechars:100 }}
                                    </p>
                                {% endif %}
                            {% endwith %}
                            <small class="text-muted">{{ conversation.messages.count }} messages</small>
                        </a>
                    {% endfor %}
                </div>

                <div class="text-center mt-3">
                    <a href="{% url 'search:conversations' %}" class="btn btn-outline-primary">View All Conversations</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5>Searching across your knowledge base...</h5>
        <p class="text-muted mb-0">This may take a few moments for complex queries</p>
        <div class="progress mt-3" style="height: 6px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Get CSRF token for AJAX requests
function getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}

document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const searchBtn = document.getElementById('searchBtn');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const queryInput = document.querySelector('input[name="query"]');

    // Handle form submission
    searchForm.addEventListener('submit', function(e) {
        // Validate form first
        const query = queryInput.value.trim();
        if (query.length < 3) {
            e.preventDefault();
            alert('Please enter at least 3 characters for your search query.');
            queryInput.focus();
            return false;
        }

        // Show loading state
        showLoadingState();

        // Allow form to submit normally
        return true;
    });

    // Handle suggestion chips
    document.querySelectorAll('.suggestion-chip').forEach(chip => {
        chip.addEventListener('click', function() {
            const query = this.getAttribute('data-query');
            queryInput.value = query;
            queryInput.focus();

            // Optional: Auto-submit the form
            // searchForm.submit();
        });
    });

    // Advanced options toggle animation
    const advancedToggle = document.querySelector('[data-bs-target="#advancedOptions"]');
    const chevronIcon = advancedToggle.querySelector('.fa-chevron-down');

    document.getElementById('advancedOptions').addEventListener('shown.bs.collapse', function() {
        chevronIcon.style.transform = 'rotate(180deg)';
    });

    document.getElementById('advancedOptions').addEventListener('hidden.bs.collapse', function() {
        chevronIcon.style.transform = 'rotate(0deg)';
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            queryInput.focus();
        }

        // Escape to clear search
        if (e.key === 'Escape' && document.activeElement === queryInput) {
            queryInput.value = '';
        }
    });

    // Auto-resize search input based on content
    queryInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.max(this.scrollHeight, 60) + 'px';
    });

    function showLoadingState() {
        // Update button state
        const btnText = searchBtn.querySelector('.btn-text');
        const spinner = searchBtn.querySelector('.spinner-border');

        btnText.textContent = 'Searching...';
        spinner.classList.remove('d-none');
        searchBtn.disabled = true;

        // Show loading overlay
        loadingOverlay.style.display = 'flex';

        // Disable form inputs (but keep CSRF token and query input enabled for submission)
        const inputs = searchForm.querySelectorAll('input, button');
        inputs.forEach(input => {
            if (input !== searchBtn &&
                input.name !== 'csrfmiddlewaretoken' &&
                input.name !== 'query') {
                input.disabled = true;
            }
        });
    }

    // Auto-focus search input
    queryInput.focus();

    // Add search input enhancements
    queryInput.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });

    queryInput.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });

    // Form validation is now handled in the main submit handler above

    // Analytics tracking (if needed)
    function trackSearchEvent(query, filters) {
        // Add analytics tracking here if needed
        console.log('Search performed:', { query, filters });
    }
});
</script>
{% endblock %}
